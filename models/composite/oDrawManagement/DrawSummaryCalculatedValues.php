<?php

namespace models\composite\oDrawManagement;

use models\types\strongType;
use models\composite\oDrawManagement\traits\PropertiesMapper;
use models\lendingwise\tblDrawRequests_h;
use models\lendingwise\db\tblDrawRequests_h_db;
use models\lendingwise\tblDrawSummaryCalculatedValues;
use models\lendingwise\tblProcessingCompanyDrawTemplateSettings;
use models\composite\oDrawManagement\DrawRequestsHistory;
use models\composite\oDrawManagement\DrawRequestManager;
use models\lendingwise\tblDrawRequestLineItems;
use models\lendingwise\db\tblDrawRequestLineItems_db;
use models\lendingwise\tblFileSimpleDrawRequests;
use models\Controllers\backoffice\LMRequest;
use models\myFileInfo;

class DrawSummaryCalculatedValues extends strongType
{
    use PropertiesMapper;

    public ?int $id = null;
    public ?int $LMRId = null;
    public ?int $drawRequestId = null;
    public ?float $totalLoanAmount = null;
    public ?float $rehabCost = null;
    public ?float $rehabCostFinanced = null;
    public ?float $currentLoanBalance = null;
    public ?float $totalDrawsFunded = null;
    public ?float $holdBackAmountRemaining = null;
    public ?tblDrawSummaryCalculatedValues $drawSummaryCalculatedValues = null;

    private ?bool $isSimpleMode = false;
    private ?bool $allowedToexceedRehabCost = false;
    private ?float $initialLoan = null;
    private ?array $drawRequestsApproved = null;
    private ?array $simpleDrawsData = null;
    private ?myFileInfo $LMRequestFileInfo = null;

    /**
     * Constructor - initializes the draw summary calculated values.
     *
     * @param int $LMRId Loan Management Request ID
     * @param int|null $drawRequestId Optional draw request ID
     */
    public function __construct(int $LMRId, int $drawRequestId = null)
    {
        $where = [
            'LMRId' => $LMRId,
        ];
        if ($drawRequestId) {
            $where['drawRequestId'] = $drawRequestId;
        }
        $dbObj = tblDrawSummaryCalculatedValues::Get($where);
        if ($dbObj === null) {
            $dbObj = new tblDrawSummaryCalculatedValues();
            $dbObj->LMRId = $LMRId;
            $dbObj->drawRequestId = $drawRequestId;
            $dbObj->save();
        }
        $this->setProperties($dbObj);
        $this->drawSummaryCalculatedValues = $dbObj;
        $PCID = DrawRequestManager::PCIDFromLMRId($LMRId);
        $templateSettings = tblProcessingCompanyDrawTemplateSettings::Get(['PCID' => $PCID]);
        $this->isSimpleMode = (bool)$templateSettings->enableSimpleMode;
        $this->allowedToexceedRehabCost = (bool)$templateSettings->allowBorrowersExceedFinancedRehabCostOnRevision;
        LMRequest::setLMRId($LMRId);
        $this->LMRequestFileInfo = LMRequest::myFileInfo();
        $this->initialLoan = (float) $this->LMRequestFileInfo->getFileCalculatedValues()->InitialLoanAmount;
    }

    /**
     * Updates all calculated values and saves to database.
     *
     * Recalculates rehab cost, financed amount, total draws funded,
     * total loan amount, current balance, and holdback remaining.
     */
    public function updateValues(): void
    {
        $this->setRehabCost();
        $this->setRehabCostFinanced();
        $this->setTotalDrawsFunded();
        $this->setTotalLoanAmount();
        $this->setCurrentLoanBalance();
        $this->setHoldBackAmountRemaining();
        $this->save();
    }

    /**
     * Saves the current state to the database.
     */
    public function save(): void
    {
        $this->drawSummaryCalculatedValues->save();
    }

    /**
     * Calculates and sets the rehabilitation cost.
     *
     * In complex mode with exceed rehab cost allowed, it will use the higher
     * of the original rehab cost or the sum of line item costs.
     */
    private function setRehabCost(): void
    {
        $rehabCost = (float) $this->LMRequestFileInfo->getFileHMLONewLoanInfo()->rehabCost;
        if (!$this->isSimpleMode && $this->allowedToexceedRehabCost) {
            $lineItems = tblDrawRequestLineItems::GetAll([
                tblDrawRequestLineItems_db::COLUMN_DRAWID => $this->drawRequestId
            ]);

            $totalCost = 0;
            foreach ($lineItems as $lineItem) {
                $totalCost += $lineItem->cost;
            }

            if ($totalCost > $rehabCost) $rehabCost = $totalCost;
        }

        $this->drawSummaryCalculatedValues->rehabCost = $rehabCost;
        $this->rehabCost = $rehabCost;
    }

    /**
     * Calculates and sets the financed portion of rehabilitation cost.
     *
     * In normal mode, it considers approved draw requests and their
     * financed percentages to determine the actual financed amount.
     */
    private function setRehabCostFinanced(): void
    {
        $rehabCostFinanced = (float) $this->LMRequestFileInfo->getFileHMLONewLoanInfo()->rehabCostFinanced;

        if (!$this->isSimpleMode) {
            $rehabCostFinanced = $this->calculateRehabCostFinancedForFixedPercentage($rehabCostFinanced);
            $calculatedRehabCostFinanced = 0;

            if (!$this->drawRequestsApproved) $this->setDrawRequestsApproved();

            foreach ($this->drawRequestsApproved as $drawRequest) {
                $rehabPercentFinanced = $drawRequest->rehabPercentFinanced;
                $amountApproved = $drawRequest->approvedAmount;
                $calculatedRehabCostFinanced += $amountApproved * $rehabPercentFinanced / 100;
            }

            if ($calculatedRehabCostFinanced > $rehabCostFinanced) $rehabCostFinanced = $calculatedRehabCostFinanced;
        }
        $this->drawSummaryCalculatedValues->rehabCostFinanced = $rehabCostFinanced;
        $this->rehabCostFinanced = $rehabCostFinanced;
    }

    /**
     * Calculates and sets the total loan amount.
     *
     * Total loan amount is the sum of initial loan and financed rehab cost.
     */
    private function setTotalLoanAmount(): void
    {
        $totalLoanAmount = $this->initialLoan + $this->rehabCostFinanced;
        $this->drawSummaryCalculatedValues->totalLoanAmount = $totalLoanAmount;
        $this->totalLoanAmount = $totalLoanAmount;
    }


    /**
     * Calculates and sets the current loan balance.
     *
     * Current balance is the initial loan plus the approved loan balance
     * from funded draws, accounting for their financed percentages.
     */
    private function setCurrentLoanBalance(): void
    {
        $approvedLoanBalance = 0;
        if ($this->isSimpleMode) {
            if (!$this->simpleDrawsData) $this->setSimpleDrawsData();
            foreach ($this->simpleDrawsData as $draw) {
                $rehabPercentFinanced = $draw->rehabPercentFinanced;
                $approvedLoanBalance += $draw->approvedAmount * $rehabPercentFinanced / 100;
            }
        } else {
            if (!$this->drawRequestsApproved) $this->setDrawRequestsApproved();
            foreach ($this->drawRequestsApproved as $drawRequest) {
                $rehabPercentFinanced = $drawRequest->rehabPercentFinanced;
                $approvedLoanBalance += $drawRequest->approvedAmount * $rehabPercentFinanced / 100;
            }
        }

        $currentLoanBalance = $this->initialLoan + $approvedLoanBalance;
        $this->drawSummaryCalculatedValues->currentLoanBalance = $currentLoanBalance;
        $this->currentLoanBalance = $currentLoanBalance;
    }


    /**
     * Calculates and sets the total amount of draws that have been funded.
     *
     * Sums up all approved draw amounts from either simple or complex mode.
     */
    private function setTotalDrawsFunded(): void
    {
        $totalDrawsFunded = 0;
        if ($this->isSimpleMode) {
            if (!$this->simpleDrawsData) $this->setSimpleDrawsData();
            foreach ($this->simpleDrawsData as $draw) {
                $totalDrawsFunded += $draw->approvedAmount;
            }
        } else {
            if (!$this->drawRequestsApproved) $this->setDrawRequestsApproved();
            foreach ($this->drawRequestsApproved as $drawRequest) {
                $totalDrawsFunded += $drawRequest->approvedAmount;
            }
        }
        $this->drawSummaryCalculatedValues->totalDrawsFunded = $totalDrawsFunded;
        $this->totalDrawsFunded = $totalDrawsFunded;
    }

    /**
     * Calculates and sets the remaining holdback amount.
     *
     * Holdback remaining is the difference between financed rehab cost
     * and total draws funded, with a minimum of 0.
     */
    private function setHoldBackAmountRemaining(): void
    {
        $holdBackAmountRemaining = $this->rehabCostFinanced - $this->totalDrawsFunded;
        $holdBackAmountRemaining = $holdBackAmountRemaining > 0 ? $holdBackAmountRemaining : 0;
        $this->drawSummaryCalculatedValues->holdBackAmountRemaining = $holdBackAmountRemaining;
        $this->holdBackAmountRemaining = $holdBackAmountRemaining;
    }

    /**
     * Calculates the rehab cost financed for fixed percentage.
     *
     * If allowed to exceed rehab cost, it recalculates the financed amount
     * based on the rehab cost percentage.
     *
     * @param float $rehabCostFinanced The original rehab cost financed
     * @return float The recalculated rehab cost financed
     */
    private function calculateRehabCostFinancedForFixedPercentage(float $rehabCostFinanced): ?float
    {
        if ($this->allowedToexceedRehabCost) {
            $rehabCostPercentageFinanced = (float) $this->LMRequestFileInfo->getFileHMLONewLoanInfo()->rehabCostPercentageFinanced;
            $calculatedRehabCostFinanced = $this->rehabCost * $rehabCostPercentageFinanced / 100;
            if ($calculatedRehabCostFinanced > $rehabCostFinanced) $rehabCostFinanced = $calculatedRehabCostFinanced;
        }

        return $rehabCostFinanced;
    }

    /**
     * Retrieves all approved draw requests.
     *
     * @return void
     */
    private function setDrawRequestsApproved(): void
    {
        $this->drawRequestsApproved = tblDrawRequests_h::GetAll([
            tblDrawRequests_h_db::COLUMN_DRAWID => $this->drawRequestId,
            tblDrawRequests_h_db::COLUMN_STATUS => DrawRequestsHistory::STATUS_APPROVED,
            tblDrawRequests_h_db::COLUMN_ISDRAWREQUEST => 1
        ]);
    }

    /**
     * Retrieves all approved simple draws data.
     *
     * @return void
     */
    private function setSimpleDrawsData(): void
    {
        $this->simpleDrawsData = tblFileSimpleDrawRequests::GetAll(['LMRId' => $this->LMRId, 'status' => 'approved']);
    }

}
