<?php
namespace models\composite\oDrawManagement;

use models\types\strongType;
use models\composite\oDrawManagement\traits\PropertiesMapper;
use models\lendingwise\tblDrawRequests_h;
use models\lendingwise\tblDrawRequestLineItems_h;
use models\lendingwise\db\tblDrawRequestLineItems_h_db;
use models\composite\oDrawManagement\BorrowerDrawLineItemHistory;
use models\lendingwise\tblProcessingCompanyDrawTemplateSettings;
use models\lendingwise\tblFileDrawRequests;
use models\Controllers\backoffice\LMRequest;
use models\PageVariables;
use models\standard\Dates;

class DrawRequestsHistory extends strongType
{
    use PropertiesMapper;

    public ?int $id = null;
    public ?int $drawId = null;
    public ?string $status = null;
    public ?int $isDrawRequest = null;
    public ?string $submittedAt = null;
    public ?string $approvedAt = null;
    public ?float $requestedAmount = 0.00;
    public ?float $approvedAmount = 0.00;
    public ?float $rehabPercentFinanced = 0.00;
    public ?float $drawFee = 0.00;
    public ?float $wireAmount = null;
    public ?string $wireSentDate = null;
    public ?string $createdAt = null;
    public ?tblDrawRequests_h $drawRequestHistory = null;
    public array $lineItems = [];

    public const STATUS_PENDING = 'pending';
    public const STATUS_APPROVED = 'approved';
    public const STATUS_REJECTED = 'rejected';

    /**
     * DrawRequestsHistory constructor.
     * @param tblDrawRequests_h|null $drawRequestHistory The database draw request history object to initialize from.
     */
    public function __construct(?tblDrawRequests_h $drawRequestHistory = null) {
        if ($drawRequestHistory == null) $drawRequestHistory = new tblDrawRequests_h();
        $this->setProperties($drawRequestHistory);
        if ($drawRequestHistory->id) $this->loadLineItems();
    }

    /**
     * Saves the current draw request history object to the database.
     * @param array|null $drawRequestData Optional data to set before saving.
     * @return array The result of the save operation.
     */
    public function save(?array $drawRequestData = null): array {
        if(!empty($drawRequestData)) $this->setFromArray($drawRequestData);
        $saved = $this->drawRequestHistory->save();
        $this->id = $this->drawRequestHistory->id;
        return $saved;
    }

    /**
     * Sets properties from an array.
     * @param array $data The data array.
     * @return void
     */
    private function setFromArray(array $data): void {
        if (isset($data['drawId'])) $this->drawRequestHistory->drawId = $data['drawId'];
        if (isset($data['status'])) $this->drawRequestHistory->status = $data['status'];
        if (isset($data['isDrawRequest'])) $this->drawRequestHistory->isDrawRequest = $data['isDrawRequest'];
        if (isset($data['submittedAt'])) $this->drawRequestHistory->submittedAt = $data['submittedAt'];
        if (isset($data['approvedAt'])) $this->drawRequestHistory->approvedAt = $data['approvedAt'];
        if (isset($data['requestedAmount'])) $this->drawRequestHistory->requestedAmount = $data['requestedAmount'];
        if (isset($data['approvedAmount'])) $this->drawRequestHistory->approvedAmount = $data['approvedAmount'];
        if (isset($data['rehabPercentFinanced'])) $this->drawRequestHistory->rehabPercentFinanced = $data['rehabPercentFinanced'];
        if (isset($data['drawFee'])) $this->drawRequestHistory->drawFee = $data['drawFee'];
        if (isset($data['wireAmount'])) $this->drawRequestHistory->wireAmount = $data['wireAmount'];
        if (isset($data['wireSentDate'])) $this->drawRequestHistory->wireSentDate = $data['wireSentDate'];
        $this->setProperties($this->drawRequestHistory);
    }

    /**
     * Load line items associated with this draw request history record.
     * @return void
     */
    private function loadLineItems(): void {
        if (!$this->id) {
            return;
        }

        $lineItemsData = tblDrawRequestLineItems_h::GetAll([
            tblDrawRequestLineItems_h_db::COLUMN_RECORDID => $this->id
        ]);

        $this->lineItems = [];
        foreach ($lineItemsData as $lineItemData) {
            $this->lineItems[] = new BorrowerDrawLineItemHistory($lineItemData);
        }
    }

    /**
     * Gets the database object.
     * @return tblDrawRequests_h The database object.
     */
    public function getDbObject(): tblDrawRequests_h {
        return $this->drawRequestHistory;
    }

    /**
     * Get all line items associated with this draw request history.
     * @return BorrowerDrawLineItemHistory[] Array of line item history objects.
     */
    public function getLineItems(): array {
        return $this->lineItems;
    }

    /**
     * Add a line item history record to this draw request history.
     * @param array $lineItemData The line item data.
     * @return BorrowerDrawLineItemHistory The created line item history object.
     */
    public function addLineItem(array $lineItemData): BorrowerDrawLineItemHistory {
        $lineItemData['recordId'] = $this->id;
        $lineItem = new BorrowerDrawLineItemHistory();
        $lineItem->save($lineItemData);
        $this->lineItems[] = $lineItem;
        return $lineItem;
    }

    /**
     * Create a new draw request history record when a draw request is approved and submitted.
     * @param int $drawId The original draw request ID.
     * @param string $status The status of the draw request.
     * @param array $lineItemsData The line items data.
     * @return self The created draw request history object.
     */
    public static function createHistoryRecord(int $drawId, string $status, int $isDrawRequest, array $lineItemsData = []): self {
        $templateData = tblProcessingCompanyDrawTemplateSettings::Get(['PCID' => PageVariables::$PCID]);

        $drawRequestData = tblFileDrawRequests::Get(['id' => $drawId]);
        $LMRId = $drawRequestData->LMRId;
        LMRequest::setLMRId($LMRId);
        $rehabCostPercentageFinanced = (float) LMRequest::myFileInfo()->getFileHMLONewLoanInfo()->rehabCostPercentageFinanced;

        $historyData = [
            'drawId' => $drawId,
            'status' => $status,
            'isDrawRequest' => $isDrawRequest,
            'rehabPercentFinanced' => $isDrawRequest ? $rehabCostPercentageFinanced : 0,
            'drawFee' => $isDrawRequest ? $templateData->drawFee : 0,
            'submittedAt' => Dates::Timestamp(),
            'requestedAmount' => self::calculateTotalRequestedAmount($lineItemsData, $isDrawRequest)
        ];

        $history = new self();
        $history->save($historyData);

        return $history;
    }

    /**
     * Get all history records for a specific draw request.
     * @param int $drawId The draw request ID.
     * @return array Array of draw request history data.
     */
    public static function getHistoryByDrawId(int $drawId, bool $toArray = false): array {
        $historyData = tblDrawRequests_h::GetAll([
            'drawId' => $drawId
        ], ['createdAt' => 'ASC']);

        $historyRecords = [];
        foreach ($historyData as $record) {
            $recordObj = new self($record);
            $historyRecords[] = $toArray ? $recordObj->toArray() : $recordObj;
        }

        return $historyRecords;
    }

    /**
     * Get all history objects for a specific draw request.
     * @param int $drawId The draw request ID.
     * @return self[] Array of draw request history objects.
     */
    private static function getHistoryObjectsByDrawId(int $drawId): array {
        $historyData = tblDrawRequests_h::GetAll([
            'drawId' => $drawId
        ], ['createdAt' => 'ASC']);

        $historyRecords = [];
        foreach ($historyData as $record) {
            $recordObj = new self($record);
            $historyRecords[] = $recordObj;
        }

        return $historyRecords;
    }

    /**
     * Get a specific history record by its ID.
     * @param int $historyId The history record ID.
     * @return self|null The history record object or null if not found.
     */
    public static function getHistoryById(int $historyId): ?self {
        $historyData = tblDrawRequests_h::Get(['id' => $historyId]);

        if (!$historyData) {
            return null;
        }

        return new self($historyData);
    }

    /**
     * Update the latest history record with approval/rejection information.
     * @param int $drawId The draw request ID.
     * @param string $status The new status.
     * @param array $lineItemsData The line items data.
     * @return bool True if updated successfully, false otherwise.
     */
    public static function updateLatestHistoryRecord(int $drawId, string $status, array $lineItemsData = []): bool
    {
        $historyRecords = self::getHistoryObjectsByDrawId($drawId);
        if (empty($historyRecords)) {
            return false;
        }

        $latestHistory = end($historyRecords);
        $updateData = ['status' => $status];

        if ($status === self::STATUS_APPROVED) {
            $approvedAmount = self::calculateTotalApprovedAmount($lineItemsData, $latestHistory->isDrawRequest);
            $rehabPercentFinanced = $latestHistory->rehabPercentFinanced;
            $drawFee = $latestHistory->drawFee;
            $wireAmount = $approvedAmount ? ($approvedAmount * $rehabPercentFinanced / 100) - $drawFee : 0;
            $updateData['approvedAmount'] = $approvedAmount;
            $updateData['approvedAt'] = Dates::Timestamp();
            $updateData['wireAmount'] = $wireAmount;
            self::createLineItemsHistory($latestHistory->id, $lineItemsData);
        }
        if($status === self::STATUS_PENDING) {
            $updateData['requestedAmount'] = self::calculateTotalRequestedAmount($lineItemsData, $latestHistory->isDrawRequest);
        }
        $latestHistory->save($updateData);
        return true;
    }

    /**
     * Create line items history records for an approved draw request.
     * @param int $historyRecordId The history record ID.
     * @param array $lineItemsData The line items data.
     * @return void
     */
    private static function createLineItemsHistory(int $historyRecordId, array $lineItemsData): void
    {
        foreach ($lineItemsData as $lineItemId => $lineItemData) {
            $historyData = [
                'recordId' => $historyRecordId,
                'lineItemId' => (int)$lineItemId,
                'completedAmount' => $lineItemData['completedAmount'] ?? 0,
                'completedPercent' => $lineItemData['completedPercent'] ?? 0,
                'requestedAmount' => $lineItemData['requestedAmount'] ?? 0,
                'disbursedAmount' => $lineItemData['disbursedAmount'] ?? 0,
                'notes' => $lineItemData['notes'] ?? null,
                'lenderNotes' => $lineItemData['lenderNotes'] ?? null
            ];

            $lineItemHistory = new BorrowerDrawLineItemHistory();
            $lineItemHistory->save($historyData);
        }
    }

    /**
     * Calculate total requested amount from line items.
     * @param array $lineItemsData The line items data.
     * @return float The total requested amount.
     */
    private static function calculateTotalRequestedAmount(array $lineItemsData, ?bool $isDrawRequest = null): float
    {
        $total = 0.0;
        foreach ($lineItemsData as $lineItemData) {
            $amount = $isDrawRequest === false ? $lineItemData['completedAmount'] : $lineItemData['requestedAmount'];
            $total += (float)($amount ?? 0);
        }
        return $total;
    }

    /**
     * Calculate total approved amount from line items.
     * @param array $lineItemsData The line items data.
     * @return float The total approved amount.
     */
    private static function calculateTotalApprovedAmount(array $lineItemsData, ?bool $isDrawRequest = null): float
    {
        $total = 0.0;
        foreach ($lineItemsData as $lineItemData) {
            $amount = $isDrawRequest === false ?  $lineItemData['completedAmount'] : $lineItemData['approvedAmount'];
            $total += (float)($amount ?? 0);
        }
        return $total;
    }

    /**
     * Converts the draw request history object to an associative array.
     * @return array An associative array representation of the draw request history.
     */
    public function toArray(): array {
        $lineItemsData = [];
        foreach ($this->lineItems as $lineItem) {
            $lineItemsData[] = $lineItem->toArray();
        }

        return [
            "id" => $this->id,
            "drawId" => $this->drawId,
            "isDrawRequest" => $this->isDrawRequest,
            "status" => $this->status,
            "submittedAt" => $this->submittedAt,
            "approvedAt" => $this->approvedAt,
            "requestedAmount" => $this->requestedAmount,
            "approvedAmount" => $this->approvedAmount,
            "rehabPercentFinanced" => $this->rehabPercentFinanced,
            "drawFee" => $this->drawFee,
            "wireAmount" => $this->wireAmount,
            "wireSentDate" => $this->wireSentDate,
            "createdAt" => $this->createdAt,
            "lineItems" => $lineItemsData
        ];
    }
}
