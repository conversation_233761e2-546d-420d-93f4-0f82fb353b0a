DrawManagement.lender = {
    init: function() {
        DrawManagement.config.pcid = $('#pcid').val();
        DrawManagement.config.dataKey = 'pcid';
        DrawManagement.config.saveLineItemsSuccessMessage = 'Line items saved successfully!';
        this.initTemplateSettingsFormSubmitHandler();
        this.handleAllowBorrowersDeleteLineItemsTog();
    },

    renderLineItemRowsUI: function(lineItems, $tbodyElement) {
        const rowTemplate = document.getElementById('line-item-row-template');
        if (!rowTemplate) {
            console.error("line-item-row-template not found!");
            return;
        }

        if (lineItems && lineItems.length > 0) {
            lineItems.forEach(item => {
                const clone = rowTemplate.content.cloneNode(true);
                const $row = $(clone).find('tr');

                $row.attr('data-line-item-id', item.id);
                $row.find('.line-item-name-display').text(item.name);
                $row.find('.line-item-name-input').val(item.name);
                $row.find('.line-item-name-display').attr('title', item.description);
                $row.find('.line-item-description-input').val(item.description);
                $row.find('.line-item-description-display').text(item.description);

                $tbodyElement.append($row);
            });
        }
    },

    initTemplateSettings: function() {
        const settings = DrawManagement.currentTemplateData;
        $('#allowBorrowersAddEditCategoriesTog').prop('checked', settings.allowBorrowersAddEditCategories === 1);
        $('#allowBorrowersDeleteCategoriesTog').prop('checked', settings.allowBorrowersDeleteCategories === 1);
        $('#allowBorrowersAddEditLineItemsTog').prop('checked', settings.allowBorrowersAddEditLineItems === 1);
        $('#allowBorrowersDeleteLineItemsTog').prop('checked', settings.allowBorrowersDeleteLineItems === 1);
        $('#allowBorrowersSOWRevisionsTog').prop('checked', settings.allowBorrowersSOWRevisions === 1);
        $('#allowBorrowersExceedFinancedRehabCostOnRevisionTog').prop('checked', settings.allowBorrowersExceedFinancedRehabCostOnRevision === 1);
        $('#allowUsersDeleteUploadsTog').prop('checked', settings.allowUsersDeleteUploads === 1);
        $('#enableSimpleModeTog').prop('checked', settings.enableSimpleMode === 1);
        $('#allowBorrowersAddEditCategories').val(settings.allowBorrowersAddEditCategories);
        $('#allowBorrowersDeleteCategories').val(settings.allowBorrowersDeleteCategories);
        $('#allowBorrowersAddEditLineItems').val(settings.allowBorrowersAddEditLineItems);
        $('#allowBorrowersDeleteLineItems').val(settings.allowBorrowersDeleteLineItems);
        $('#allowBorrowersSOWRevisions').val(settings.allowBorrowersSOWRevisions);
        $('#allowBorrowersExceedFinancedRehabCostOnRevision').val(settings.allowBorrowersExceedFinancedRehabCostOnRevision);
        $('#allowUsersDeleteUploads').val(settings.allowUsersDeleteUploads);
        $('#enableSimpleMode').val(settings.enableSimpleMode);
        $('#drawFee').val(settings.drawFee);

        // Update child permissions after settings are loaded
        this.updateChildPermissionsDisplay();
    },

    initTemplateSettingsFormSubmitHandler: function() {
        $('#drawTemplateSettingsForm').on('submit', function(e) {
            e.preventDefault();

            const $form = $(this);
            const $submitBtn = $form.find('.save-settings');
            const originalText = 'Save';

            $submitBtn.prop('disabled', true).text('Saving...');

            const formDataArray = $form.serializeArray();
            const dataObject = {};
            $.each(formDataArray, function(_, field) {
                dataObject[field.name] = field.value;
            });
            drawManagementApi.saveTemplateSettings(dataObject)
                .then(function(response) {
                    if (response.success) {
                        toastrNotification('Template settings saved successfully!', 'success');
                    } else {
                        toastrNotification('Error saving settings: ' + (response.message || 'Unknown error'), 'error');
                    }
                })
                .catch(function(error) {
                    console.error('API Error:', error);
                    toastrNotification('Error saving settings. Please try again.', 'error');
                })
                .always(function() {
                    $submitBtn.prop('disabled', false).text(originalText);
                });
        });
    },

    handleAllowBorrowersDeleteLineItemsTog: function() {
        $('#allowBorrowersSOWRevisionsTog').on('change', this.updateChildPermissionsDisplay);
    },

    updateChildPermissionsDisplay: function() {
        const isParentChecked = $('#allowBorrowersSOWRevisionsTog').is(':checked');
        const $childToggle = $('#allowBorrowersExceedFinancedRehabCostOnRevisionTog');
        const $childInput = $('#allowBorrowersExceedFinancedRehabCostOnRevision');

        if (!isParentChecked) {
            $childToggle.prop('checked', false);
            $childInput.val(0);
        }
        $('.exceedRehabCostDiv').toggle(isParentChecked);
    }
};



