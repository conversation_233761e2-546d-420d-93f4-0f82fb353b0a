/**
 * LoanFileDrawRequestManager Class
 * Manages draw request functionality for loan files including status changes,
 * validation, lender notes, and PDF export functionality
 */
class LoanFileDrawRequestManager {
    constructor(config = {}) {
        this.config = {
            $saveBtn: config.$saveBtn || $('#btnSave'),
            sowApproved: config.sowApproved || 0,
            statusApproved: config.statusApproved || 'approved',
            statusRejected: config.statusRejected || 'rejected',
            LMRId: config.LMRId || null,
            currentStatus: config.currentStatus || ''
        };

        this.lenderNotesState = {
            currentLineItemId: null,
            currentButton: null
        };

        this.init();
    }

    /**
     * Initialize the draw request manager
     */
    init() {
        this.setupEventHandlers();
        this.handleStatusChange();
        this.initializeLenderNotesModal();
        this.validateSaveButton();
    }

    /**
     * Set up all event handlers
     */
    setupEventHandlers() {
        $('.statusAction').on('change', () => this.handleStatusChange());
        DrawRequestUtils.setupInputHandlers(() => this.validateSaveButton());
        var self = this;
        this.config.$saveBtn.on('click', (e) => {
            const statusText = $('.statusAction > option:selected').text().toLowerCase();
            const type = statusText === 'accept' ? 'green' : 'red';
            e.preventDefault();
                $.confirm({
                icon: 'fa fa-warning',
                closeIcon: true,
                title: 'Update Status',
                content: 'Are you sure you want to ' + statusText + '?',
                type: type,
                backgroundDismiss: true,
                buttons: {
                    yes: {
                        text: 'Yes',
                        action: function () {
                            self.submitDrawManagementData();
                        }
                    },
                    cancel: {
                        text: 'Cancel',
                        action: function () {
                        }
                    },
                },
                onClose: function () {
                },
            });
        });

        $('#exportTableBtn').on('click', (e) => {
            e.preventDefault();
            this.exportTableToPdf();
        });

        $('input[name="rehabPercentFinanced"], input[name="drawFee"]').on('input', () => {
            DrawRequestUtils.updateHistoryTableAmounts();
        });
    }

    /**
     * Handle status change events
     */
    handleStatusChange() {
        const selectedValue = $('.statusAction').val();
        if (selectedValue === 'rejected' || this.config.currentStatus === 'rejected') {
            $('.col-reject-reason').removeClass('hide');
        } else {
            $('.col-reject-reason').addClass('hide');
        }
        this.validateSaveButton();
    }

    /**
     * Validate and enable/disable save button based on current state
     */
    validateSaveButton() {
        const selectedStatus = $('.statusAction').val();

        if (selectedStatus === this.config.statusApproved && this.config.sowApproved) {
            const hasValidationErrors = DrawRequestUtils.hasValidationErrors();
            this.config.$saveBtn.prop('disabled', hasValidationErrors);
        }
        else {
            this.config.$saveBtn.prop('disabled', false);
        }
    }

    /**
     * Initialize lender notes modal functionality
     */
    initializeLenderNotesModal() {
        const $buttons = $('.lender-note-btn');
        if ($buttons.length === 0) {
            return;
        }

        $(document).on('click', '.lender-note-btn', (e) => {
            e.preventDefault();
            e.stopPropagation();

            const $btn = $(e.currentTarget);
            this.lenderNotesState.currentLineItemId = $btn.data('line-item-id');
            this.lenderNotesState.currentButton = $btn;
            const currentNote = $btn.find('i').data('original-title') || '';
            $('#lenderNotesTextarea').val(currentNote);
            $('#lenderNotesModal').modal('show');
        });

        $('#saveLenderNote').on('click', () => {
            this.saveLenderNote();
        });

        $('#lenderNotesModal').on('hidden.bs.modal', () => {
            this.resetLenderNotesState();
        });
    }

    /**
     * Save lender note and update UI
     */
    saveLenderNote() {
        const newNote = $('#lenderNotesTextarea').val().trim();

        if (this.lenderNotesState.currentButton) {
            const $icon = this.lenderNotesState.currentButton.find('i');
            $icon.data('original-title', newNote);
            $icon.attr('data-original-title', newNote);

            if (newNote) {
                $icon.removeClass('text-muted').addClass('text-primary');
            } else {
                $icon.removeClass('text-primary').addClass('text-muted');
            }
        }

        $('#lenderNotesModal').modal('hide');
    }

    /**
     * Reset lender notes modal state
     */
    resetLenderNotesState() {
        this.lenderNotesState.currentLineItemId = null;
        this.lenderNotesState.currentButton = null;
        $('#lenderNotesTextarea').val('');
    }

    /**
     * Collect line item data from the form
     * @returns {Object} Object containing line items data
     */
    collectLineItemsData() {
        const status = $('#status').val();

        // Use state management only
        if (!window.drawManagementState) {
            console.error('State management not available');
            return {};
        }

        const stateLineItems = window.drawManagementState.getAllLineItems();
        const lineItems = {};

        Object.keys(stateLineItems).forEach(categoryId => {
            stateLineItems[categoryId].forEach(lineItem => {
                const lineItemData = {
                    id: lineItem.id,
                    notes: lineItem.notes || '',
                    lenderNotes: lineItem.lenderNotes || '',
                    rejectReason: status === 'rejected' ? (lineItem.rejectReason || '') : ''
                };

                if (lineItem.requestedAmount !== undefined) {
                    lineItemData.requestedAmount = lineItem.requestedAmount;
                }

                lineItems[lineItem.id] = lineItemData;
            });
        });

        return lineItems;
    }

    /**
     * Submit draw management data
     */
    submitDrawManagementData() {
        const status = $('#status').val();
        const lineItems = this.collectLineItemsData();

        const drawRequestData = {
            LMRId: this.config.LMRId,
            status: status,
            lineItems: lineItems
        };

        if($('.history-table').find('tr[data-history-id]').length) {
            drawRequestData.historyData = this.collectHistoryData();
        }

        if (!this.validateDrawRequestData(drawRequestData)) {
            return;
        }

        this.sendDrawRequestData(drawRequestData);
    }

    /**
     * Validate draw request data
     * @param {Object} drawRequestData - Data to validate
     * @returns {boolean} True if valid, false otherwise
     */
    validateDrawRequestData(drawRequestData) {
        const validator = new Validator();
        const validationContext = {
            userType: 'lender',
            isDraft: false,
            isDrawRequest: true
        };

        if (!validator.validateForm(drawRequestData, 'drawRequest', validationContext)) {
            const errors = validator.getErrors();
            toastrNotification('Validation Error: ' + errors.join(', '), 'error');
            return false;
        }

        return true;
    }

    collectHistoryData() {
        const historyData = {};
        let $row = $('.history-table').find('tr[data-history-id]');
        historyData.id = parseInt($row.data('history-id'));
        historyData.rehabPercentFinanced = parseFloat($('.history-table').find('input[name="rehabPercentFinanced"]').val());
        historyData.drawFee = parseFloat($('.history-table').find('input[name="drawFee"]').val()) || 0;
        return historyData;
    }

    /**
     * Send draw request data to API
     * @param {Object} drawRequestData - Data to send
     */
    sendDrawRequestData(drawRequestData) {
        const sanitizedData = DataMapper.sanitizeObject(drawRequestData);
        this.config.$saveBtn.prop('disabled', true).text('Saving...');

        drawManagementApi.saveDrawRequest(sanitizedData)
            .then((response) => {
                this.handleSaveResponse(response);
            })
            .catch((error) => {
                this.handleSaveError(error);
            })
            .always(() => {
                this.config.$saveBtn.prop('disabled', false).text('Save');
            });
    }

    /**
     * Handle successful save response
     * @param {Object} response - API response
     */
    handleSaveResponse(response) {
        if (response.success) {
            toastrNotification('Draw Request Status Updated!', 'success');
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            toastrNotification('Error: ' + (response.message || 'Failed to update Draw Request Status'), 'error');
        }
    }

    /**
     * Handle save error
     * @param {Error} error - Error object
     */
    handleSaveError(error) {
        console.error('API Error:', error);
        let errorMsg = error.message || 'An error occurred while saving data. Please try again.';
        toastrNotification(errorMsg, 'error');
    }

    /**
     * Export table to PDF
     */
    exportTableToPdf() {
        const $exportBtn = $('#exportTableBtn');
        const originalText = $exportBtn.html();
        $exportBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Generating PDF...');

        const exportData = {
            LMRId: this.config.LMRId
        };

        const sanitizedData = DataMapper.sanitizeObject(exportData);

        drawManagementApi.exportToPdf(sanitizedData)
            .then((response) => {
                this.handlePdfResponse(response);
            })
            .catch((error) => {
                this.handlePdfError(error);
            })
            .always(() => {
                $exportBtn.prop('disabled', false).html(originalText);
            });
    }

    /**
     * Handle PDF export response
     * @param {Object} response - API response
     */
    handlePdfResponse(response) {
        if (response.success) {
            this.downloadPdfBlob(response.data);
            toastrNotification('PDF exported successfully!', 'success');
        } else {
            toastrNotification('Error: ' + (response.data.message || 'Failed to generate PDF'), 'error');
        }
    }

    /**
     * Handle PDF export error
     * @param {Error} error - Error object
     */
    handlePdfError(error) {
        console.error('PDF Export Error:', error);
        let errorMsg = error.message || 'An error occurred while generating the PDF. Please try again.';
        toastrNotification(errorMsg, 'error');
    }

    /**
     * Download PDF blob
     * @param {Object} data - PDF data from response
     */
    downloadPdfBlob(data) {
        const byteCharacters = atob(data.pdf_data);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: 'application/pdf' });

        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = data.filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    }

    /**
     * Update configuration
     * @param {Object} newConfig - New configuration values
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
    }

    /**
     * Get current configuration
     * @returns {Object} Current configuration
     */
    getConfig() {
        return { ...this.config };
    }
}
