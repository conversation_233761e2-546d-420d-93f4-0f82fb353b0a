/**
 * State Management System for Draw Management Data
 * Handles categories and line items without DOM dependency
 */
class DrawManagementState {
    constructor() {
        this.categories = new Map();
        this.lineItems = new Map(); // categoryId -> Map(lineItemId -> lineItem)
        this.listeners = new Set();
        this.nextTempId = 1;
    }

    // Category Management
    addCategory(categoryData) {
        const id = categoryData.id || `temp_${this.nextTempId++}`;
        const category = {
            id: id,
            categoryName: categoryData.categoryName || '',
            description: categoryData.description || '',
            order: categoryData.order || this.categories.size + 1,
            ...categoryData
        };
        this.categories.set(id, category);
        this.notifyListeners('categoryAdded', { category });
        return category;
    }

    updateCategory(id, updates) {
        id = parseInt(id);
        if (this.categories.has(id)) {
            const category = { ...this.categories.get(id), ...updates };
            this.categories.set(id, category);
            this.notifyListeners('categoryUpdated', { category });
            return category;
        }
        return null;
    }

    removeCategory(id) {
        id = parseInt(id);
        if (this.categories.has(id)) {
            const category = this.categories.get(id);
            this.categories.delete(id);
            // Remove associated line items
            this.lineItems.delete(id);
            this.notifyListeners('categoryRemoved', { category });
            return true;
        }
        return false;
    }

    getCategory(id) {
        id = parseInt(id);
        return this.categories.get(id);
    }

    getAllCategories() {
        return Array.from(this.categories.values()).sort((a, b) => a.order - b.order);
    }

    // Line Item Management
    addLineItem(categoryId, lineItemData) {
        categoryId = parseInt(categoryId);

        if (!this.lineItems.has(categoryId)) {
            this.lineItems.set(categoryId, new Map());
        }

        const id = lineItemData.id || `temp_${this.nextTempId++}`;
        const lineItem = {
            id: id,
            categoryId: categoryId,
            name: lineItemData.name || '',
            description: lineItemData.description || '',
            order: lineItemData.order || (this.lineItems.get(categoryId).size + 1),
            cost: parseFloat(lineItemData.cost) || 0,
            completedAmount: parseFloat(lineItemData.completedAmount) || 0,
            completedPercent: parseFloat(lineItemData.completedPercent) || 0,
            requestedAmount: parseFloat(lineItemData.requestedAmount) || 0,
            requestedPercent: parseFloat(lineItemData.requestedPercent) || 0,
            disbursedAmount: parseFloat(lineItemData.disbursedAmount) || 0,
            notes: lineItemData.notes || '',
            lenderNotes: lineItemData.lenderNotes || '',
            ...lineItemData
        };

        this.lineItems.get(categoryId).set(id, lineItem);
        this.notifyListeners('lineItemAdded', { lineItem });
        return lineItem;
    }

    updateLineItem(categoryId, id, updates) {
        categoryId = parseInt(categoryId);
        if (this.lineItems.has(categoryId) && this.lineItems.get(categoryId).has(id)) {
            const lineItem = { ...this.lineItems.get(categoryId).get(id), ...updates };
            this.lineItems.get(categoryId).set(id, lineItem);
            this.notifyListeners('lineItemUpdated', { lineItem });
            return lineItem;
        }
        return null;
    }

    removeLineItem(categoryId, id) {
        categoryId = parseInt(categoryId);
        if (this.lineItems.has(categoryId) && this.lineItems.get(categoryId).has(id)) {
            const lineItem = this.lineItems.get(categoryId).get(id);
            this.lineItems.get(categoryId).delete(id);
            this.notifyListeners('lineItemRemoved', { lineItem });
            return true;
        }
        return false;
    }

    getLineItem(categoryId, id) {
        categoryId = parseInt(categoryId);
        id = parseInt(id);
        return this.lineItems.get(categoryId)?.get(id);
    }

    getLineItemsForCategory(categoryId) {
        categoryId = parseInt(categoryId);
        const categoryLineItems = this.lineItems.get(categoryId);
        if (!categoryLineItems) return [];
        return Array.from(categoryLineItems.values()).sort((a, b) => a.order - b.order);
    }

    getAllLineItems() {
        const result = {};
        for (const [categoryId, lineItemsMap] of this.lineItems) {
            result[categoryId] = Array.from(lineItemsMap.values()).sort((a, b) => a.order - b.order);
        }
        return result;
    }

    // Event System
    addListener(callback) {
        this.listeners.add(callback);
        return () => this.listeners.delete(callback);
    }

    notifyListeners(event, data) {
        this.listeners.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Error in state listener:', error);
            }
        });
    }

    // Data Import/Export
    loadFromData(data) {
        this.clear();

        if (data.categories) {
            data.categories.forEach(category => {
                this.addCategory(category);
                if (category.lineItems) {
                    category.lineItems.forEach(lineItem => {
                        this.addLineItem(category.id, lineItem);
                    });
                }
            });
        }
    }

    clear() {
        this.categories.clear();
        this.lineItems.clear();
        this.notifyListeners('cleared', {});
    }

    clearCategories() {
        this.categories.clear();
        this.lineItems.clear(); // Clear line items too since they depend on categories
        this.notifyListeners('categoriesCleared', {});
    }

    // Export data in the format expected by the backend
    exportForBackend() {
        const categories = this.getAllCategories();
        const lineItems = this.getAllLineItems();

        return {
            categories: categories,
            lineItems: lineItems
        };
    }
}

class DataMapper {
    static propertyMaps = {
        category: {
            frontend: ['id', 'categoryName', 'description', 'order', 'lineItems'],
            backend: ['id', 'categoryName', 'description', 'order', 'lineItems'],
            mapping: {}
        },
        lineItem: {
            frontend: ['id', 'categoryId', 'name', 'description', 'order', 'cost', 'completedAmount',
                      'completedPercent', 'requestedAmount', 'requestedPercent', 'disbursedAmount','notes', 'lenderNotes'],
            backend: ['id', 'categoryId', 'name', 'description', 'order', 'cost', 'completedAmount',
                     'completedPercent', 'requestedAmount', 'requestedPercent', 'disbursedAmount', 'notes', 'lenderNotes'],
            mapping: {}
        },
        drawRequest: {
            frontend: ['id', 'LMRId', 'status', 'categories', 'amountRequested', 'lenderNotes'],
            backend: ['id', 'LMRId', 'status', 'categories', 'amountRequested', 'lenderNotes'],
            mapping: {}
        }
    };

    // Global state instance
    static state = new DrawManagementState();

    static mapObject(sourceObject, objectType, direction = 'toBackend') {
        if (!sourceObject || typeof sourceObject !== 'object') {
            return {};
        }

        const config = this.propertyMaps[objectType];
        if (!config) {
            console.warn(`Unknown object type: ${objectType}`);
            return sourceObject;
        }

        const mappedObject = {};
        const sourceProperties = direction === 'toBackend' ? config.frontend : config.backend;
        const mapping = config.mapping;

        sourceProperties.forEach(property => {
            const mappedProperty = mapping[property] || property;

            if (sourceObject.hasOwnProperty(property)) {
                let value = sourceObject[property];

                if (property === 'lineItems' && Array.isArray(value)) {
                    value = value.map(item => this.mapObject(item, 'lineItem', direction));
                } else if (property === 'categories' && Array.isArray(value)) {
                    value = value.map(item => this.mapObject(item, 'category', direction));
                }

                value = this.convertValue(value, property, direction);

                mappedObject[mappedProperty] = value;
            }
        });

        return mappedObject;
    }

    static convertValue(value, property, direction) {
        if (value === null || value === undefined) {
            return null;
        }

        if (['id', 'order', 'LMRId'].includes(property)) {
            return value === '' ? null : parseInt(value, 10);
        }

        if (['cost', 'completedAmount', 'completedPercent', 'requestedAmount',
             'requestedPercent', 'amountRequested'].includes(property)) {
            return value === '' ? 0 : parseFloat(value);
        }

        if (['categoryName', 'description', 'name', 'notes', 'lenderNotes', 'status'].includes(property)) {
            return String(value);
        }

        return value;
    }



    // New state-based methods (preferred)
    static buildCategoriesRequestFromState(config) {
        const categories = this.state.getAllCategories();
        const mappedCategories = categories.map(cat => this.mapObject(cat, 'category', 'toBackend'));

        const request = {
            categories: mappedCategories
        };

        if (config.dataKey && config[config.dataKey]) {
            request[config.dataKey] = config[config.dataKey];
        }

        return request;
    }

    static buildLineItemsRequestFromState(config) {
        const lineItems = this.state.getAllLineItems();
        const mappedLineItems = {};

        Object.keys(lineItems).forEach(categoryId => {
            mappedLineItems[categoryId] = lineItems[categoryId].map(
                item => this.mapObject(item, 'lineItem', 'toBackend')
            );
        });

        const request = {
            lineItems: mappedLineItems,
            isDraft: config.isDraft || false
        };

        if (config.dataKey && config[config.dataKey]) {
            request[config.dataKey] = config[config.dataKey];
        }

        return request;
    }

    // State-based methods (now the only methods)
    static buildCategoriesRequest(config) {
        return this.buildCategoriesRequestFromState(config);
    }

    static buildLineItemsRequest(config) {
        return this.buildLineItemsRequestFromState(config);
    }

    static buildDrawRequestRequest(formData, config) {
        const drawRequest = this.mapObject(formData, 'drawRequest', 'toBackend');

        if (config.LMRId) {
            drawRequest.LMRId = config.LMRId;
        }

        return drawRequest;
    }

    // State synchronization utilities
    static syncFormToState($form, type, categoryId = null) {
        const formData = this.extractFormData($form);

        if (type === 'category') {
            if (formData.id) {
                this.state.updateCategory(formData.id, formData);
            } else {
                this.state.addCategory(formData);
            }
        } else if (type === 'lineItem' && categoryId) {
            if (formData.id) {
                this.state.updateLineItem(categoryId, formData.id, formData);
            } else {
                this.state.addLineItem(categoryId, formData);
            }
        }
    }

    static syncStateToForm($form, type, id, categoryId = null) {
        let data;

        if (type === 'category') {
            data = this.state.getCategory(id);
        } else if (type === 'lineItem' && categoryId) {
            data = this.state.getLineItem(categoryId, id);
        }

        if (data) {
            this.populateForm($form, data);
        }
    }

    static extractFormData($form) {
        const formData = {};

        $form.find('input, select, textarea').each(function() {
            const $field = $(this);
            const name = $field.attr('name') || $field.data('field');

            if (name) {
                let value = $field.val();

                // Convert numeric fields
                if ($field.attr('type') === 'number' || $field.hasClass('numeric')) {
                    value = parseFloat(value) || 0;
                }

                formData[name] = value;
            }
        });

        // Extract data attributes
        const id = $form.data('id') || $form.find('[data-id]').data('id');
        if (id) formData.id = id;

        return formData;
    }

    static populateForm($form, data) {
        Object.keys(data).forEach(key => {
            const $field = $form.find(`[name="${key}"], [data-field="${key}"]`);
            if ($field.length) {
                $field.val(data[key]);
            }
        });

        // Set data attributes
        if (data.id) {
            $form.data('id', data.id);
        }
    }

    // Data binding for real-time updates
    static bindFormToState($form, type, categoryId = null) {
        const self = this;

        $form.on('input change', 'input, select, textarea', function() {
            // Debounce updates
            clearTimeout(self._updateTimeout);
            self._updateTimeout = setTimeout(() => {
                self.syncFormToState($form, type, categoryId);
            }, 300);
        });
    }

    static bindStateToUI(callback) {
        return this.state.addListener(callback);
    }

    static sanitizeObject(object) {
        if (!object || typeof object !== 'object') {
            return object;
        }

        const sanitized = {};

        Object.keys(object).forEach(key => {
            let value = object[key];

            if (Array.isArray(value)) {
                value = value.map(item => this.sanitizeObject(item));
            } else if (typeof value === 'object' && value !== null) {
                value = this.sanitizeObject(value);
            } else if (typeof value === 'string') {
                value = value.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
            }

            sanitized[key] = value;
        });

        return sanitized;
    }
}

// Global instances
window.DataMapper = DataMapper;
window.DrawManagementState = DrawManagementState;

// Initialize global state if not already done
if (!window.drawManagementState) {
    window.drawManagementState = DataMapper.state;
}
