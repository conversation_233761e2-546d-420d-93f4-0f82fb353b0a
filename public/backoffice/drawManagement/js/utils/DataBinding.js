/**
 * Data Binding Utilities for Draw Management
 * Handles two-way data binding between forms and state
 */
class DataBinding {
    constructor(state = window.drawManagementState) {
        this.state = state;
        this.bindings = new Map();
        this.updateTimeouts = new Map();
    }

    /**
     * Bind a category form to state
     */
    bindCategoryForm($form, categoryId = null) {
        const bindingId = this.generateBindingId('category', categoryId);
        
        // Remove existing binding if any
        this.unbind(bindingId);
        
        const binding = {
            type: 'category',
            $form: $form,
            categoryId: categoryId,
            listeners: []
        };

        // Bind form inputs to state updates
        const inputHandler = () => {
            this.debounceUpdate(bindingId, () => {
                this.syncCategoryFormToState($form, categoryId);
            });
        };

        $form.on('input.databinding change.databinding', 'input, select, textarea', inputHandler);
        binding.listeners.push(() => $form.off('.databinding'));

        // Bind state changes to form updates
        const stateListener = (event, data) => {
            if (event === 'categoryUpdated' && data.category.id === categoryId) {
                this.syncCategoryStateToForm($form, data.category);
            }
        };

        const removeStateListener = this.state.addListener(stateListener);
        binding.listeners.push(removeStateListener);

        this.bindings.set(bindingId, binding);
        return bindingId;
    }

    /**
     * Bind a line item form to state
     */
    bindLineItemForm($form, categoryId, lineItemId = null) {
        const bindingId = this.generateBindingId('lineItem', lineItemId, categoryId);
        
        // Remove existing binding if any
        this.unbind(bindingId);
        
        const binding = {
            type: 'lineItem',
            $form: $form,
            categoryId: categoryId,
            lineItemId: lineItemId,
            listeners: []
        };

        // Bind form inputs to state updates
        const inputHandler = () => {
            this.debounceUpdate(bindingId, () => {
                this.syncLineItemFormToState($form, categoryId, lineItemId);
            });
        };

        $form.on('input.databinding change.databinding', 'input, select, textarea', inputHandler);
        binding.listeners.push(() => $form.off('.databinding'));

        // Bind state changes to form updates
        const stateListener = (event, data) => {
            if (event === 'lineItemUpdated' && 
                data.lineItem.categoryId === categoryId && 
                data.lineItem.id === lineItemId) {
                this.syncLineItemStateToForm($form, data.lineItem);
            }
        };

        const removeStateListener = this.state.addListener(stateListener);
        binding.listeners.push(removeStateListener);

        this.bindings.set(bindingId, binding);
        return bindingId;
    }

    /**
     * Bind an entire category section (category + line items)
     */
    bindCategorySection($section, categoryId) {
        const bindings = [];
        
        // Bind category form
        const $categoryForm = $section.find('.category-form, .category-header-form');
        if ($categoryForm.length) {
            bindings.push(this.bindCategoryForm($categoryForm, categoryId));
        }

        // Bind line item forms
        $section.find('.line-item-form, .editable-line-item').each((index, element) => {
            const $lineItemForm = $(element);
            const lineItemId = $lineItemForm.data('line-item-id');
            if (lineItemId) {
                bindings.push(this.bindLineItemForm($lineItemForm, categoryId, lineItemId));
            }
        });

        return bindings;
    }

    /**
     * Sync category form data to state
     */
    syncCategoryFormToState($form, categoryId) {
        const formData = this.extractFormData($form);
        
        if (categoryId && this.state.getCategory(categoryId)) {
            this.state.updateCategory(categoryId, formData);
        } else if (!categoryId) {
            const category = this.state.addCategory(formData);
            $form.data('category-id', category.id);
        }
    }

    /**
     * Sync category state to form
     */
    syncCategoryStateToForm($form, categoryData) {
        this.populateForm($form, categoryData);
    }

    /**
     * Sync line item form data to state
     */
    syncLineItemFormToState($form, categoryId, lineItemId) {
        const formData = this.extractFormData($form);
        formData.categoryId = categoryId;
        
        if (lineItemId && this.state.getLineItem(categoryId, lineItemId)) {
            this.state.updateLineItem(categoryId, lineItemId, formData);
        } else if (!lineItemId) {
            const lineItem = this.state.addLineItem(categoryId, formData);
            $form.data('line-item-id', lineItem.id);
        }
    }

    /**
     * Sync line item state to form
     */
    syncLineItemStateToForm($form, lineItemData) {
        this.populateForm($form, lineItemData);
    }

    /**
     * Extract form data from jQuery form element
     */
    extractFormData($form) {
        const formData = {};
        
        $form.find('input, select, textarea').each(function() {
            const $field = $(this);
            const name = $field.attr('name') || $field.data('field');
            
            if (name && !$field.hasClass('ignore-binding')) {
                let value = $field.val();
                
                // Convert numeric fields
                if ($field.attr('type') === 'number' || $field.hasClass('numeric')) {
                    value = parseFloat(value) || 0;
                }
                
                // Handle checkboxes
                if ($field.attr('type') === 'checkbox') {
                    value = $field.is(':checked');
                }
                
                formData[name] = value;
            }
        });
        
        // Extract data from display elements
        $form.find('[data-field]').each(function() {
            const $element = $(this);
            const field = $element.data('field');
            if (field && !formData.hasOwnProperty(field)) {
                formData[field] = $element.text().trim();
            }
        });
        
        return formData;
    }

    /**
     * Populate form with data
     */
    populateForm($form, data) {
        Object.keys(data).forEach(key => {
            const $field = $form.find(`[name="${key}"], [data-field="${key}"]`);
            
            if ($field.length) {
                if ($field.is('input[type="checkbox"]')) {
                    $field.prop('checked', !!data[key]);
                } else if ($field.is('input, select, textarea')) {
                    $field.val(data[key]);
                } else {
                    $field.text(data[key]);
                }
            }
        });
    }

    /**
     * Debounce updates to prevent excessive state changes
     */
    debounceUpdate(bindingId, callback, delay = 300) {
        if (this.updateTimeouts.has(bindingId)) {
            clearTimeout(this.updateTimeouts.get(bindingId));
        }
        
        const timeout = setTimeout(() => {
            callback();
            this.updateTimeouts.delete(bindingId);
        }, delay);
        
        this.updateTimeouts.set(bindingId, timeout);
    }

    /**
     * Generate unique binding ID
     */
    generateBindingId(type, id, categoryId = null) {
        if (type === 'lineItem') {
            return `${type}_${categoryId}_${id || 'new'}`;
        }
        return `${type}_${id || 'new'}`;
    }

    /**
     * Remove binding
     */
    unbind(bindingId) {
        const binding = this.bindings.get(bindingId);
        if (binding) {
            // Clean up event listeners
            binding.listeners.forEach(cleanup => cleanup());
            this.bindings.delete(bindingId);
            
            // Clear any pending timeouts
            if (this.updateTimeouts.has(bindingId)) {
                clearTimeout(this.updateTimeouts.get(bindingId));
                this.updateTimeouts.delete(bindingId);
            }
        }
    }

    /**
     * Remove all bindings
     */
    unbindAll() {
        this.bindings.forEach((binding, bindingId) => {
            this.unbind(bindingId);
        });
    }

    /**
     * Get binding information
     */
    getBinding(bindingId) {
        return this.bindings.get(bindingId);
    }

    /**
     * Get all bindings
     */
    getAllBindings() {
        return Array.from(this.bindings.values());
    }
}

// Global instance
window.DataBinding = DataBinding;

// Create default instance
if (!window.dataBinding) {
    window.dataBinding = new DataBinding();
}
