/**
 * Debug test for state management issues
 * This file helps debug why categories and line items are not updating in state
 */

class StateDebugTest {
    constructor() {
        this.testResults = [];
    }

    /**
     * Run debug tests
     */
    runDebugTests() {
        console.log('🔍 Starting State Debug Tests...');
        
        this.testStateInitialization();
        this.testCategoryOperations();
        this.testLineItemOperations();
        this.testStateListeners();
        
        this.printResults();
    }

    /**
     * Test state initialization
     */
    testStateInitialization() {
        console.log('🚀 Testing State Initialization...');
        
        try {
            // Check if state is available
            this.assert(window.drawManagementState, 'drawManagementState should be available on window');
            this.assert(window.DataMapper, 'DataMapper should be available on window');
            this.assert(window.dataBinding, 'dataBinding should be available on window');
            
            // Check state methods
            this.assert(typeof window.drawManagementState.addCategory === 'function', 'addCategory method should exist');
            this.assert(typeof window.drawManagementState.addLineItem === 'function', 'addLineItem method should exist');
            this.assert(typeof window.drawManagementState.getAllCategories === 'function', 'getAllCategories method should exist');
            this.assert(typeof window.drawManagementState.getAllLineItems === 'function', 'getAllLineItems method should exist');
            
            console.log('✅ State Initialization tests passed');
            
        } catch (error) {
            console.error('❌ State Initialization test failed:', error);
            this.testResults.push({ test: 'State Initialization', passed: false, error: error.message });
        }
    }

    /**
     * Test category operations
     */
    testCategoryOperations() {
        console.log('📁 Testing Category Operations...');
        
        try {
            // Clear state first
            window.drawManagementState.clear();
            
            // Test adding category
            const categoryData = {
                id: 'test_cat_' + Date.now(),
                categoryName: 'Test Category',
                description: 'Test Description',
                order: 1
            };
            
            const addedCategory = window.drawManagementState.addCategory(categoryData);
            this.assert(addedCategory, 'Category should be added');
            this.assert(addedCategory.id === categoryData.id, 'Category ID should match');
            this.assert(addedCategory.categoryName === categoryData.categoryName, 'Category name should match');
            
            // Test getting categories
            const categories = window.drawManagementState.getAllCategories();
            this.assert(categories.length === 1, 'Should have one category');
            this.assert(categories[0].id === categoryData.id, 'Retrieved category should match added category');
            
            // Test updating category
            const updatedCategory = window.drawManagementState.updateCategory(categoryData.id, {
                categoryName: 'Updated Category Name'
            });
            this.assert(updatedCategory.categoryName === 'Updated Category Name', 'Category should be updated');
            
            // Test removing category
            window.drawManagementState.removeCategory(categoryData.id);
            const categoriesAfterRemove = window.drawManagementState.getAllCategories();
            this.assert(categoriesAfterRemove.length === 0, 'Should have no categories after removal');
            
            console.log('✅ Category Operations tests passed');
            
        } catch (error) {
            console.error('❌ Category Operations test failed:', error);
            this.testResults.push({ test: 'Category Operations', passed: false, error: error.message });
        }
    }

    /**
     * Test line item operations
     */
    testLineItemOperations() {
        console.log('📝 Testing Line Item Operations...');
        
        try {
            // Clear state and add a category first
            window.drawManagementState.clear();
            const category = window.drawManagementState.addCategory({
                id: 'test_cat_' + Date.now(),
                categoryName: 'Test Category',
                description: 'Test Description',
                order: 1
            });
            
            // Test adding line item
            const lineItemData = {
                id: 'test_li_' + Date.now(),
                name: 'Test Line Item',
                cost: 1000,
                completedAmount: 500,
                completedPercent: 50,
                requestedAmount: 250,
                notes: 'Test notes'
            };
            
            const addedLineItem = window.drawManagementState.addLineItem(category.id, lineItemData);
            this.assert(addedLineItem, 'Line item should be added');
            this.assert(addedLineItem.id === lineItemData.id, 'Line item ID should match');
            this.assert(addedLineItem.name === lineItemData.name, 'Line item name should match');
            
            // Test getting line items
            const lineItems = window.drawManagementState.getAllLineItems();
            this.assert(lineItems[category.id], 'Should have line items for category');
            this.assert(lineItems[category.id].length === 1, 'Should have one line item');
            this.assert(lineItems[category.id][0].id === lineItemData.id, 'Retrieved line item should match added line item');
            
            // Test updating line item
            const updatedLineItem = window.drawManagementState.updateLineItem(category.id, lineItemData.id, {
                name: 'Updated Line Item Name',
                cost: 2000
            });
            this.assert(updatedLineItem.name === 'Updated Line Item Name', 'Line item name should be updated');
            this.assert(updatedLineItem.cost === 2000, 'Line item cost should be updated');
            
            // Test removing line item
            window.drawManagementState.removeLineItem(category.id, lineItemData.id);
            const lineItemsAfterRemove = window.drawManagementState.getAllLineItems();
            this.assert(!lineItemsAfterRemove[category.id] || lineItemsAfterRemove[category.id].length === 0, 'Should have no line items after removal');
            
            console.log('✅ Line Item Operations tests passed');
            
        } catch (error) {
            console.error('❌ Line Item Operations test failed:', error);
            this.testResults.push({ test: 'Line Item Operations', passed: false, error: error.message });
        }
    }

    /**
     * Test state listeners
     */
    testStateListeners() {
        console.log('👂 Testing State Listeners...');
        
        try {
            let eventsFired = [];
            
            // Add listener
            const listener = (event, data) => {
                eventsFired.push({ event, data });
            };
            
            window.drawManagementState.addListener(listener);
            
            // Clear state
            window.drawManagementState.clear();
            
            // Add category (should fire event)
            const category = window.drawManagementState.addCategory({
                id: 'test_cat_' + Date.now(),
                categoryName: 'Test Category',
                description: 'Test Description',
                order: 1
            });
            
            // Add line item (should fire event)
            window.drawManagementState.addLineItem(category.id, {
                id: 'test_li_' + Date.now(),
                name: 'Test Line Item',
                cost: 1000
            });
            
            // Check if events were fired
            this.assert(eventsFired.length >= 2, 'Should have fired at least 2 events (category and line item added)');
            
            const categoryAddedEvent = eventsFired.find(e => e.event === 'categoryAdded');
            const lineItemAddedEvent = eventsFired.find(e => e.event === 'lineItemAdded');
            
            this.assert(categoryAddedEvent, 'Should have fired categoryAdded event');
            this.assert(lineItemAddedEvent, 'Should have fired lineItemAdded event');
            
            console.log('✅ State Listeners tests passed');
            
        } catch (error) {
            console.error('❌ State Listeners test failed:', error);
            this.testResults.push({ test: 'State Listeners', passed: false, error: error.message });
        }
    }

    /**
     * Assert helper
     */
    assert(condition, message) {
        if (!condition) {
            throw new Error(`Assertion failed: ${message}`);
        }
        this.testResults.push({ test: message, passed: true });
    }

    /**
     * Print test results
     */
    printResults() {
        console.log('\n📋 Debug Test Results Summary:');
        
        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;
        
        console.log(`✅ Passed: ${passed}/${total}`);
        
        const failed = this.testResults.filter(r => !r.passed);
        if (failed.length > 0) {
            console.log('❌ Failed tests:');
            failed.forEach(f => {
                console.log(`  - ${f.test}: ${f.error}`);
            });
        }
        
        if (passed === total) {
            console.log('🎉 All debug tests passed! State management is working correctly.');
        } else {
            console.log('⚠️ Some tests failed. State management may have issues.');
        }
        
        // Additional debug info
        console.log('\n🔍 Current State Debug Info:');
        console.log('Categories:', window.drawManagementState ? window.drawManagementState.getAllCategories() : 'State not available');
        console.log('Line Items:', window.drawManagementState ? window.drawManagementState.getAllLineItems() : 'State not available');
    }

    /**
     * Test the actual UI integration
     */
    testUIIntegration() {
        console.log('\n🖥️ Testing UI Integration...');
        
        try {
            // Test if DrawManagement is available
            this.assert(window.DrawManagement, 'DrawManagement should be available');
            
            // Test if buildCategoriesJson works
            const categoriesJson = window.DrawManagement.buildCategoriesJson();
            console.log('Categories JSON:', categoriesJson);
            
            // Test if buildLineItemsJson works
            const lineItemsJson = window.DrawManagement.buildLineItemsJson();
            console.log('Line Items JSON:', lineItemsJson);
            
            console.log('✅ UI Integration test completed');
            
        } catch (error) {
            console.error('❌ UI Integration test failed:', error);
            this.testResults.push({ test: 'UI Integration', passed: false, error: error.message });
        }
    }
}

// Export for use in development
window.StateDebugTest = StateDebugTest;

// Auto-run tests in development mode
if (window.location.hostname === 'localhost' || window.location.hostname.includes('dev')) {
    $(document).ready(() => {
        // Add a small delay to ensure all scripts are loaded
        setTimeout(() => {
            if (window.drawManagementState) {
                const test = new StateDebugTest();
                test.runDebugTests();
                test.testUIIntegration();
            } else {
                console.warn('⚠️ drawManagementState not available. Cannot run debug tests.');
                console.log('Available on window:', Object.keys(window).filter(k => k.includes('draw') || k.includes('Data')));
            }
        }, 2000);
    });
}
