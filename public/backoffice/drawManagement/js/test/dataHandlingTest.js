/**
 * Test suite for the refactored data handling system
 * This file can be included in development to test the new state management system
 */

class DataHandlingTest {
    constructor() {
        this.testResults = [];
    }

    /**
     * Run all tests
     */
    runAllTests() {
        console.log('🧪 Starting Data Handling Tests...');
        
        this.testStateManagement();
        this.testDataBinding();
        this.testBackwardCompatibility();
        this.testDataMapping();
        
        this.printResults();
    }

    /**
     * Test state management functionality
     */
    testStateManagement() {
        console.log('📊 Testing State Management...');
        
        try {
            // Test category management
            const state = new DrawManagementState();
            
            // Add category
            const category = state.addCategory({
                categoryName: 'Test Category',
                description: 'Test Description',
                order: 1
            });
            
            this.assert(category.id, 'Category should have an ID');
            this.assert(category.categoryName === 'Test Category', 'Category name should be set');
            
            // Update category
            const updated = state.updateCategory(category.id, { categoryName: 'Updated Category' });
            this.assert(updated.categoryName === 'Updated Category', 'Category should be updated');
            
            // Add line item
            const lineItem = state.addLineItem(category.id, {
                name: 'Test Line Item',
                cost: 1000,
                completedAmount: 500,
                completedPercent: 50
            });
            
            this.assert(lineItem.id, 'Line item should have an ID');
            this.assert(lineItem.cost === 1000, 'Line item cost should be set');
            
            // Get line items for category
            const lineItems = state.getLineItemsForCategory(category.id);
            this.assert(lineItems.length === 1, 'Should have one line item');
            
            console.log('✅ State Management tests passed');
            
        } catch (error) {
            console.error('❌ State Management test failed:', error);
            this.testResults.push({ test: 'State Management', passed: false, error: error.message });
        }
    }

    /**
     * Test data binding functionality
     */
    testDataBinding() {
        console.log('🔗 Testing Data Binding...');
        
        try {
            // Create test form
            const $testForm = $(`
                <form class="test-form">
                    <input name="categoryName" value="Test Category" />
                    <input name="description" value="Test Description" />
                    <input type="hidden" name="id" value="test-id" />
                </form>
            `);
            
            $('body').append($testForm);
            
            const dataBinding = new DataBinding();
            
            // Test form data extraction
            const formData = dataBinding.extractFormData($testForm);
            this.assert(formData.categoryName === 'Test Category', 'Should extract category name');
            this.assert(formData.description === 'Test Description', 'Should extract description');
            
            // Test form population
            dataBinding.populateForm($testForm, {
                categoryName: 'Updated Category',
                description: 'Updated Description'
            });
            
            this.assert($testForm.find('[name="categoryName"]').val() === 'Updated Category', 'Should populate form fields');
            
            // Cleanup
            $testForm.remove();
            
            console.log('✅ Data Binding tests passed');
            
        } catch (error) {
            console.error('❌ Data Binding test failed:', error);
            this.testResults.push({ test: 'Data Binding', passed: false, error: error.message });
        }
    }

    /**
     * Test backward compatibility
     */
    testBackwardCompatibility() {
        console.log('🔄 Testing Backward Compatibility...');
        
        try {
            // Test that old API still works
            const config = { useStateManagement: false, dataKey: 'pcid', pcid: 123 };
            
            // Create mock DOM structure
            const $container = $(`
                <div class="categories-container">
                    <div class="category-item" data-category-id="1">
                        <span class="category-name">Test Category</span>
                        <span class="category-description">Test Description</span>
                    </div>
                </div>
            `);
            
            $('body').append($container);
            
            // Test old buildCategoriesRequest method
            const result = DataMapper.buildCategoriesRequest($container, config);
            
            this.assert(result.categories, 'Should return categories');
            this.assert(result.categories.length > 0, 'Should have categories');
            this.assert(result.pcid === 123, 'Should include config data');
            
            // Cleanup
            $container.remove();
            
            console.log('✅ Backward Compatibility tests passed');
            
        } catch (error) {
            console.error('❌ Backward Compatibility test failed:', error);
            this.testResults.push({ test: 'Backward Compatibility', passed: false, error: error.message });
        }
    }

    /**
     * Test data mapping functionality
     */
    testDataMapping() {
        console.log('🗺️ Testing Data Mapping...');
        
        try {
            const testCategory = {
                id: 1,
                categoryName: 'Test Category',
                description: 'Test Description',
                order: 1
            };
            
            // Test mapping to backend
            const mapped = DataMapper.mapObject(testCategory, 'category', 'toBackend');
            
            this.assert(mapped.id === 1, 'Should map ID');
            this.assert(mapped.categoryName === 'Test Category', 'Should map category name');
            
            // Test state-based request building
            DataMapper.state.clear();
            DataMapper.state.addCategory(testCategory);
            
            const request = DataMapper.buildCategoriesRequestFromState({ dataKey: 'pcid', pcid: 123 });
            
            this.assert(request.categories, 'Should build categories request');
            this.assert(request.categories.length === 1, 'Should have one category');
            this.assert(request.pcid === 123, 'Should include config data');
            
            console.log('✅ Data Mapping tests passed');
            
        } catch (error) {
            console.error('❌ Data Mapping test failed:', error);
            this.testResults.push({ test: 'Data Mapping', passed: false, error: error.message });
        }
    }

    /**
     * Assert helper
     */
    assert(condition, message) {
        if (!condition) {
            throw new Error(`Assertion failed: ${message}`);
        }
        this.testResults.push({ test: message, passed: true });
    }

    /**
     * Print test results
     */
    printResults() {
        console.log('\n📋 Test Results Summary:');
        
        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;
        
        console.log(`✅ Passed: ${passed}/${total}`);
        
        const failed = this.testResults.filter(r => !r.passed);
        if (failed.length > 0) {
            console.log('❌ Failed tests:');
            failed.forEach(f => {
                console.log(`  - ${f.test}: ${f.error}`);
            });
        }
        
        if (passed === total) {
            console.log('🎉 All tests passed! The refactored system is working correctly.');
        } else {
            console.log('⚠️ Some tests failed. Please review the implementation.');
        }
    }
}

// Export for use in development
window.DataHandlingTest = DataHandlingTest;

// Auto-run tests in development mode
if (window.location.hostname === 'localhost' || window.location.hostname.includes('dev')) {
    $(document).ready(() => {
        // Add a small delay to ensure all scripts are loaded
        setTimeout(() => {
            if (window.DataMapper && window.DrawManagementState && window.DataBinding) {
                const test = new DataHandlingTest();
                test.runAllTests();
            } else {
                console.warn('⚠️ Data handling classes not fully loaded. Skipping tests.');
            }
        }, 1000);
    });
}
