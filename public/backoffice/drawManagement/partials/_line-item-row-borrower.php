<!-- Template for a single Line Item Row for Borrower-->
<template id="line-item-row-template">
    <tr class="editable-line-item line-item-form" data-line-item-id="">
        <td>
            <span class="line-item-display line-item-name-display" data-field="name"></span>
            <input type="text" class="form-control line-item-input line-item-name-input d-none" name="name" value="">
            <input type="hidden" name="id" data-field="id" />
            <input type="hidden" name="categoryId" data-field="categoryId" />
            <input type="hidden" name="order" data-field="order" />
        </td>
        <td>
            <div class="input-group">
                <div class="input-group-prepend">
                    <span class="input-group-text bold">$</span>
                </div>
                <input type="number" name="cost" class="form-control line-item-cost-input numeric" placeholder="0" min="0"
                    step="100" inputmode="decimal" data-field="cost">
            </div>
        </td>
        <td>
            <div class="input-group">
                <div class="input-group-prepend">
                    <span class="input-group-text bold">%</span>
                </div>
                <input type="number" name="completedPercent" class="form-control line-item-completed-percentage-input numeric"
                    placeholder="0" min="0" max="100" step="5" inputmode="decimal" data-field="completedPercent">
            </div>
        </td>
        <td>
            <div class="input-group">
                <div class="input-group-prepend">
                    <span class="input-group-text bold">$</span>
                </div>
                <input type="number" name="completedAmount" class="form-control line-item-completed-amount-input numeric"
                    placeholder="0" min="0" step="100" inputmode="decimal" data-field="completedAmount">
                <input type="hidden" name="disbursedAmount" class="line-item-disbursed-amount" value="0" data-field="disbursedAmount">
                <input type="hidden" name="requestedAmount" data-field="requestedAmount" value="0">
                <input type="hidden" name="requestedPercent" data-field="requestedPercent" value="0">
            </div>
        </td>
        <td class="text-center">
            <div class="note-container">
                <button type="button" class="btn p-0 note-btn" data-toggle="modal" data-target="#noteModal"
                    data-note="" tabindex="-1" data-field="notes">
                    <i class="icon-md fas fa-comment-medical fa-lg"></i>
                </button>
                <div class="popover" style="display: none;"></div>
                <input type="hidden" name="notes" data-field="notes" value="">
            </div>
        </td>
        <td class="text-center">
            <div class="note-container">
                <button type="button" class="btn p-0 note-btn lender-notes" data-note="" data-original-note="" tabindex="-1" data-field="lenderNotes">
                    <i class="icon-md fas fa-comment-medical fa-lg"></i>
                </button>
                <div class="popover" style="display: none;"></div>
                <input type="hidden" name="lenderNotes" data-field="lenderNotes" value="">
            </div>
        </td>
        <td class="text-center">
            <div class="note-container">
                <button type="button" class="btn p-0 note-btn description-btn" data-toggle="modal"
                    data-target="#noteModal" data-note="" tabindex="-1" data-field="description">
                    <i class="fas fa-align-left fa-lg"></i>
                </button>
                <div class="popover" style="display: none;"></div>
                <input type="hidden" name="description" data-field="description" value="">
            </div>
        </td>
        <td class="text-center col-action">
            <a href="#" class="remove-line-item-btn text-muted" tabindex="-1"><i class="fas fa-trash-alt"></i></a>
            <a href="#" class="upload-doc-btn btn-link ml-3" tabindex="-1" title="Upload Documents">
                <i class="fas fa-upload"></i>
            </a>
            <input type="file" class="d-none line-item-file-input" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif,.bmp,.txt,.xls,.xlsx">
        </td>
    </tr>
</template>
