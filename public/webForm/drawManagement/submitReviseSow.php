<?php
use models\portals\PublicPage;
use models\JSCompiler;
use models\standard\Strings;
use models\composite\oDrawManagement\SowTemplateManager;
use models\composite\oDrawManagement\DrawRequestManager;
use models\composite\oDrawManagement\DrawRequest;
use models\cypher;
use models\Request;
use models\Controllers\backoffice\LMRequest;

session_start();
require '../../includes/util.php';

$pcid = Request::GetClean('pcid') ?? 0;
if (!is_numeric($pcid)) {
    $pcid = cypher::myDecryption($pcid);
}
$LMRId = Request::GetClean('LMRId');
if (!is_numeric($LMRId)) {
    $LMRId = (int)cypher::myDecryption($LMRId);
}
$templateManager = SowTemplateManager::forProcessingCompany($pcid);
$template = $templateManager->getTemplate();
$templateData = $templateManager->getTemplateDataArray();
LMRequest::setLMRId($LMRId);

$drawRequestManager = DrawRequestManager::forLoanFile($LMRId);
$drawRequest = $drawRequestManager->getDrawRequest();

$templateData['rehabCost'] = LMRequest::myFileInfo()->fileHMLOInfo()->rehabCost ?? 0;
$templateData['isInitialScopeOfWork'] = $drawRequestManager->isInitialScopeOfWork();
SowTemplateManager::setUserType(SowTemplateManager::USER_BORROWER);

if($drawRequest) {
    switch($drawRequest->status) {
        case DrawRequest::STATUS_PENDING:
            $displayStatus = $drawRequest->isDrawRequest ? 'Your draw request has been submitted and is awaiting approval by the lender.' : 'Your scope of work has been submitted and is awaiting approval by the lender.';
            $displayStatusClass = 'alert-warning';
            break;
        case DrawRequest::STATUS_APPROVED:
            $displayStatus = $drawRequest->isDrawRequest ? 'Draw request approved.' : 'Scope of work approved.';
            $displayStatusClass = 'alert-success';
            break;
        case DrawRequest::STATUS_REJECTED:
            $displayStatus = 'Please review the revision requests under lender notes. Once complete use the "Submit for Approval" button';
            $displayStatusClass = 'alert-danger';
            break;
        }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <link href="/assets/images/favicon-whitelabel.png" rel="SHORTCUT ICON" />
    <title> Submit/Revise Scope of Work</title>
    <?php
    PublicPage::Init();
    echo JSCompiler::scripts();
    echo JSCompiler::stylesheets();
    Strings::includeMyScript([
        '/backoffice/drawManagement/js/utils/ApiClient.js',
        '/backoffice/drawManagement/js/utils/DataMapper.js',
        '/backoffice/drawManagement/js/utils/DataBinding.js',
        '/backoffice/drawManagement/js/utils/Validator.js',
    ]);
    if(!$drawRequest->sowApproved) {
        Strings::includeMyScript([
            '/backoffice/drawManagement/js/common.js',
            '/backoffice/drawManagement/js/borrower.js',
            '/assets/js/3rdParty/sortable/Sortable.min.js'
        ]);

    }
    Strings::includeMyCSS([
        '/backoffice/drawManagement/css/drawManagement.css',
        '/backoffice/drawManagement/loanFile/css/drawSummary.css',
    ]);
    ?>
</head>

<body translate="no">
        <div class="container mt-4 mb-5">
        <!-- Main Content Card -->
        <div class="card p-4 p-md-5 shadow-sm">
            <?php if($drawRequest->sowApproved) {
                if ($template->allowBorrowersSOWRevisions && $drawRequest->status === DrawRequest::STATUS_APPROVED) {
                    require 'drawOptions.php';
                } else {

                    require 'drawRequest.php';
                }
            } else {
                require '../../backoffice/drawManagement/partials/_draw-management-card.php';
            }
            ?>

            <input type="hidden" id="LMRId" value="<?php echo htmlspecialchars($_REQUEST['LMRId'] ?? ''); ?>">
        </div>

        <!-- Budget Sidebar -->
        <?php if (!$drawRequest->sowApproved && !$templateData['isInitialScopeOfWork']): ?>
            <?php require '../../backoffice/drawManagement/partials/_budget-sidebar.php'; ?>
        <?php endif; ?>

        <?php
            require '../../backoffice/drawManagement/loanFile/drawSummary.php';
            require('../../backoffice/drawManagement/loanFile/drawHistory.php');
        ?>
    </div>
    <?php if(!$drawRequest->isDrawRequest): ?>
    <script>
        window.templateData = <?= json_encode($templateData) ?>;

        $(document).ready(function() {
            if (typeof DrawManagement !== 'undefined') {
                DrawManagement.init(`<?= SowTemplateManager::$userType ?>`, `<?= json_encode($templateData) ?>`);
            }
        });
    </script>
    <?php endif; ?>
</body>

</html>
